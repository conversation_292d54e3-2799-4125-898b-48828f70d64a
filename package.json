{"name": "offshore-calendar-web-1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.13", "@types/nanoid": "^3.0.0", "@types/pako": "^2.0.4", "@upstash/redis": "^1.35.3", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^11.11.11", "html2canvas": "^1.4.1", "ical-generator": "^9.0.0", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "nanoid": "^5.1.5", "next": "15.0.3", "pako": "^2.1.0", "react": "19.0.0-rc-66855b96-20241106", "react-day-picker": "^8.10.1", "react-dom": "19.0.0-rc-66855b96-20241106", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8", "eslint-config-next": "15.0.3", "playwright": "^1.54.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b"}