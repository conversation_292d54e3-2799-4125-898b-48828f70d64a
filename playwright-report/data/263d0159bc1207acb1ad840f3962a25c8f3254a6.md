# Page snapshot

```yaml
- main:
  - heading "Offshore Mate" [level=1]
  - paragraph: Navigate your offshore schedule with precision
  - button "Go back to rotation selection":
    - img
    - text: Back
  - button "Jump to today's month": Today
  - text: 🛠️ Sep 3 -> 16
  - main "Work rotation schedule":
    - region "September 2025":
      - button "Go to previous month":
        - img
      - heading "September 2025" [level=3]
      - button "Go to next month":
        - img
      - grid "Calendar for September 2025":
        - columnheader "Mon"
        - columnheader "Tue"
        - columnheader "Wed"
        - columnheader "Thu"
        - columnheader "Fri"
        - columnheader "Sat"
        - columnheader "Sun"
        - gridcell "1 off day": "1"
        - gridcell "2 transition day": "2"
        - gridcell "3 work day": "3"
        - gridcell "4 work day": "4"
        - gridcell "5 work day": "5"
        - gridcell "6 work day": "6"
        - gridcell "7 work day": "7"
        - gridcell "8 work day": "8"
        - gridcell "9 work day": "9"
        - gridcell "10 work day": "10"
        - gridcell "11 work day": "11"
        - gridcell "12 work day": "12"
        - gridcell "13 work day": "13"
        - gridcell "14 work day": "14"
        - gridcell "15 work day": "15"
        - gridcell "16 transition day": "16"
        - gridcell "17 off day": "17"
        - gridcell "18 off day": "18"
        - gridcell "19 off day": "19"
        - gridcell "20 off day": "20"
        - gridcell "21 off day": "21"
        - gridcell "22 off day": "22"
        - gridcell "23 off day": "23"
        - gridcell "24 off day": "24"
        - gridcell "25 off day": "25"
        - gridcell "26 off day": "26"
        - gridcell "27 off day": "27"
        - gridcell "28 off day": "28"
        - gridcell "29 off day": "29"
        - gridcell "30 transition day": "30"
      - img
      - text: Work Days
      - img
      - text: Off Days
      - img
      - text: Transition Days
- heading "Export Calendar" [level=3]
- button:
  - img
- radio "🖼️ PNG Image High quality image file" [checked]
- text: 🖼️ PNG Image High quality image file
- radio "📄 PDF Document Printable document format"
- text: 📄 PDF Document Printable document format
- radio "Add to Calendar Import to calendar app"
- img
- text: Add to Calendar Import to calendar app
- button "Export Calendar":
  - img
  - text: Export Calendar
- button "Export":
  - img
- button "Share":
  - img
- button "Settings":
  - img
- status:
  - img
  - text: Static route
  - button "Hide static indicator":
    - img
- alert
```