{
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "extends": ["next/core-web-vitals"],
  "rules": {
    // Disable some rules that are too strict for production builds
    "@typescript-eslint/no-unused-vars": ["warn", { 
      "argsIgnorePattern": "^_", 
      "varsIgnorePattern": "^_" 
    }],
    "@typescript-eslint/no-explicit-any": ["warn"],
    "react-hooks/exhaustive-deps": "warn"
  }
}
