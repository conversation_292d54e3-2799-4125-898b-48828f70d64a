# Docker Setup Plan for Offshore Mate Application

## Overview
Setting up Docker containerization for the Next.js 15 offshore calendar application with proper development and production configurations.

## Implementation Plan

### Phase 1: Docker Configuration Files
1. **Create production Dockerfile**
   - Multi-stage build with Node.js 23-alpine
   - Optimize for pnpm package manager
   - Configure Next.js standalone output
   - Implement security best practices (non-root user)

2. **Create development Docker setup**
   - Separate Dockerfile for development
   - Docker Compose for development environment
   - Volume mounting for hot reload

3. **Add .dockerignore**
   - Exclude unnecessary files from Docker context
   - Optimize build performance

### Phase 2: Next.js Configuration Updates
1. **Update next.config.ts**
   - Enable standalone output for optimized Docker builds
   - Ensure compatibility with containerization

### Phase 3: Testing & Validation
1. **Test Docker builds**
   - Verify production build works
   - Test development environment
   - Validate image size optimization

## Technical Specifications

### Production Setup
- **Base Image**: `node:23-alpine`
- **Package Manager**: pnpm via Corepack
- **Build Strategy**: Multi-stage build
- **Security**: Non-root user (nextjs:nodejs)
- **Output**: Next.js standalone mode
- **Expected Image Size**: ~230MB (optimized)

### Development Setup
- **Base Image**: `node:23-alpine`
- **Features**: Hot reload, volume mounting
- **Port**: 3000
- **Development server**: `pnpm dev --turbopack`

### Files to Create
1. `Dockerfile` (production)
2. `Dockerfile.dev` (development)
3. `docker-compose.yml` (development)
4. `docker-compose.prod.yml` (production)
5. `.dockerignore`

### Configuration Updates
- Update `next.config.ts` to enable standalone output

## Benefits
- Consistent development environment across team
- Simplified deployment process
- Optimized production builds
- Better resource isolation
- Easy scaling and orchestration

## Dependencies
- Docker Desktop (already running)
- Current Next.js 15 application
- pnpm package manager

## Implementation Status
✅ **Completed:**
- Production Dockerfile with multi-stage builds
- Development Dockerfile for local development
- Docker Compose configurations (dev & prod)
- .dockerignore for build optimization
- Next.js standalone output configuration

⚠️ **Docker Credential Issue:**
Docker Desktop credential helper is causing build failures. This is a common macOS Docker Desktop issue that can be resolved by:
1. Restarting Docker Desktop
2. Or using alternative base images that don't require authentication

## Usage Instructions

### Development Environment:
```bash
# Start development environment
docker compose up --build

# Or run in detached mode
docker compose up -d --build
```

### Production Build:
```bash
# Build production image
docker build -t offshore-mate:latest .

# Run production container
docker compose -f docker-compose.prod.yml up
```

### Manual Commands:
```bash
# Development
docker build -f Dockerfile.dev -t offshore-mate:dev .
docker run -p 3000:3000 -v $(pwd):/app offshore-mate:dev

# Production
docker build -t offshore-mate:prod .
docker run -p 3000:3000 offshore-mate:prod
```

