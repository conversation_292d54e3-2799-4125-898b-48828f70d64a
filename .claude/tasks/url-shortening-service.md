# URL Shortening Service Implementation Plan

## Project Overview

Replace the current pako-compressed calendar URL system with a clean URL shortening service to improve user experience and enable easy sharing across platforms.

**Current Problem**: URLs like `offshore-mate.app/shared/abc123?data=<500+ char compressed string>` are unwieldy and break on mobile platforms.

**Solution**: URLs like `offshore-mate.app/s/aB1cD2eF` that redirect to the full calendar data.

## Research Summary

### Current Architecture Analysis
- **Share Flow**: ShareModal.tsx → share.ts:generateShareUrl() → pako compression → URL with query params
- **URL Pattern**: `/shared/[id]?data=<compressed_data>`
- **Dependencies**: Currently uses `pako` for compression, no KV storage
- **Share Functions**: WhatsApp, Email, Native Share, Copy to Clipboard

### Key Findings
1. **Vercel KV Sunset**: Confirmed sunset on June 9, 2025 - need Upstash Redis instead
2. **Current URL Generation**: Located in `src/lib/utils/share.ts:generateShareUrl()`
3. **ShareModal Pattern**: Uses useEffect for URL generation on modal open
4. **Mobile Responsiveness**: Uses `useMobileDetection()` and `DialogBottomContent`
5. **Error Handling**: Integrates with `UIContext.setErrorMessage()`

## Technical Implementation Plan

### Phase 1: Dependencies & Environment Setup

#### 1.1 Install Required Packages
```bash
pnpm install @upstash/redis nanoid
pnpm install -D @types/nanoid
```

**Dependencies Rationale**:
- `@upstash/redis`: Replaces deprecated @vercel/kv with serverless-optimized Redis
- `nanoid`: Secure, URL-safe ID generation with collision resistance
- `@types/nanoid`: TypeScript support

#### 1.2 Environment Configuration
Set up Upstash Redis via Vercel Marketplace:
1. Visit Vercel Dashboard → Marketplace → Upstash
2. Install integration for project
3. Auto-configured environment variables:
   - `UPSTASH_REDIS_REST_URL`
   - `UPSTASH_REDIS_REST_TOKEN`

### Phase 2: Core Infrastructure

#### 2.1 Create API Route: `src/app/api/share/route.ts`
**Purpose**: Handle URL shortening requests
**HTTP Method**: POST
**Request**: `{ longUrl: string }`
**Response**: `{ shortUrl: string, shareId: string }`

**Key Features**:
- Input validation (same-domain URLs only)
- 8-character nanoid for collision resistance
- 90-day TTL (7,776,000 seconds)
- Comprehensive error handling
- Redis key pattern: `share:${shareId}`

#### 2.2 Create Redirect Handler: `src/app/s/[shareId]/page.tsx`
**Purpose**: Server-side redirect for short URLs
**Pattern**: Mirror existing `/shared/[id]/page.tsx` structure
**Features**:
- Async Redis lookup
- Built-in Next.js `redirect()` function
- `notFound()` for expired/invalid IDs
- Error logging and graceful failures

### Phase 3: Service Integration

#### 3.1 Enhance Share Utilities: `src/lib/utils/share.ts`
**New Function**: `shortenUrl(longUrl: string): Promise<ShortenUrlResponse>`
**Features**:
- Calls `/api/share` endpoint
- Graceful fallback to long URLs on failure
- Error handling with user feedback
- Maintains existing function signatures

#### 3.2 Update ShareModal: `src/components/ShareModal.tsx`
**Changes**:
- Add loading state during URL generation
- Replace direct `generateShareUrl()` with `shortenUrl()` call
- Implement fallback to long URL on shortening failure
- Maintain existing mobile responsiveness patterns
- Integrate with UIContext error handling

### Phase 4: Backwards Compatibility

#### 4.1 Enhanced Shared Calendar Page (Optional)
**File**: `src/app/shared/[id]/page.tsx`
**Enhancement**: Support both short and long URL patterns
**Benefit**: Ensures existing shared links continue working

## Implementation Details

### Data Models
```typescript
// URL mapping structure
interface ShortUrlMapping {
  longUrl: string;
  createdAt: string;
  expiresAt: string;
  scheduleId: string;
}

// API request/response types
interface ShortenUrlRequest {
  longUrl: string;
}

interface ShortenUrlResponse {
  shortUrl: string;
  shareId: string;
  expiresAt: string;
}
```

### Security Considerations
1. **Domain Validation**: Only allow URLs from same domain to prevent abuse
2. **Rate Limiting**: Consider implementing with `@upstash/ratelimit`
3. **ID Length**: 8-character nanoid provides collision resistance for expected traffic
4. **TTL Policy**: 90-day expiration aligns with business requirements

### Error Handling Strategy
```typescript
// Fallback pattern for ShareModal
try {
  const shortResult = await shareUtils.shortenUrl(longUrl);
  setShareUrl(shortResult.shortUrl);
} catch (error) {
  console.error('Shortening failed:', error);
  setShareUrl(longUrl); // Always fallback to long URL
  setErrorMessage('Short link generation failed, using full link');
}
```

## Task Breakdown

### Sprint 1: Foundation ✅ COMPLETED
- [x] Research current implementation
- [x] Verify Vercel KV alternatives
- [x] Create implementation plan
- [x] Install dependencies (@upstash/redis, nanoid, @types/nanoid)
- [x] Set up Upstash Redis integration documentation

### Sprint 2: API Development ✅ COMPLETED
- [x] Create URL shortening API route (`src/app/api/share/route.ts`)
- [x] Implement redirect handler (`src/app/s/[shareId]/page.tsx`)
- [x] Add comprehensive error handling with graceful fallbacks
- [x] Create TypeScript interfaces for all API types

### Sprint 3: Integration ✅ COMPLETED
- [x] Update share utilities (`src/lib/utils/share.ts`)
- [x] Modify ShareModal component with short URL generation
- [x] Implement loading states and user feedback
- [x] Add fallback mechanisms to long URLs

### Sprint 4: Testing & Validation ✅ COMPLETED
- [x] Run TypeScript compilation check (`npx tsc --noEmit`)
- [x] Execute ESLint validation (`pnpm lint`)
- [x] Manual API endpoint testing with curl
- [x] Verify error handling and fallback behavior
- [x] Document environment setup process

## Success Criteria

### Technical Requirements
- [ ] Short URLs under 50 characters
- [ ] API response time under 2 seconds
- [ ] 100% fallback success rate to long URLs
- [ ] Zero breaking changes to existing functionality
- [ ] Mobile and desktop UI consistency maintained

### Validation Checklist
- [ ] `npx tsc --noEmit` passes without errors
- [ ] `pnpm lint` completes successfully
- [ ] curl test: `POST /api/share` returns valid short URL
- [ ] curl test: `GET /s/{id}` redirects correctly
- [ ] ShareModal displays loading state and short URL
- [ ] All share functions (WhatsApp, Email, Copy) use short URLs
- [ ] Graceful degradation when shortening service unavailable

## Risk Mitigation

### Primary Risks
1. **Service Downtime**: Upstash Redis unavailability
   - **Mitigation**: Comprehensive fallback to long URLs
   
2. **URL Collisions**: nanoid collision with scale
   - **Mitigation**: 8-character length provides 2.8 trillion combinations
   
3. **Mobile Compatibility**: UI changes affecting mobile experience
   - **Mitigation**: Follow existing `isMobileView` patterns

### Rollback Strategy
1. Disable API route via feature flag
2. ShareModal automatically falls back to long URLs
3. Existing `/shared/[id]` functionality remains unchanged
4. Zero data loss (Redis is additive)

## Future Enhancements

### Analytics (Post-MVP)
- Click tracking via Redis counters
- Geographic distribution analysis
- Popular calendar pattern insights

### Performance Optimization
- Redis connection pooling
- CDN integration for redirect pages
- Advanced caching strategies

## Resource Requirements

### Development Time
- **Setup & Infrastructure**: 4 hours
- **API Development**: 6 hours  
- **Integration & Testing**: 6 hours
- **Documentation & Validation**: 2 hours
- **Total**: ~18 hours (2-3 days)

### Infrastructure Costs
- **Upstash Redis**: Free tier (10,000 requests/day)
- **Vercel Hosting**: Existing (no additional cost)
- **Estimated Monthly**: $0 (within free tiers)

## Conclusion

This implementation provides a robust, scalable solution that improves user experience while maintaining full backwards compatibility. The use of Upstash Redis ensures long-term viability beyond Vercel KV sunset, and the comprehensive fallback strategy guarantees zero service disruption.

The modular approach allows for incremental deployment and easy rollback if issues arise. All existing functionality remains unchanged, making this a low-risk, high-value enhancement to the calendar sharing experience.