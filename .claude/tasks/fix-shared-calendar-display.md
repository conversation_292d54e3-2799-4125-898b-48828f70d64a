# Fix Shared Calendar Display

## Problem Analysis

The shared calendar feature is not working because it relies on localStorage, which is browser-specific. When users open a shared link, they're accessing it from their own browser where the schedule doesn't exist in their localStorage.

### Current Flow:
1. User creates calendar → saved to their localStorage
2. User shares link → `/shared/{scheduleId}`
3. Recipient opens link → tries to fetch from their (empty) localStorage
4. Result: "Calendar Not Found" error

## Root Cause

The `getSchedule(scheduleId)` function in `/shared/[id]/page.tsx` (line 24) looks for the schedule in the recipient's localStorage, which doesn't contain the shared schedule data.

## Solution Options

### Option 1: URL-Based Data Sharing (Recommended for MVP)
- Encode calendar data in URL parameters
- Pros: No backend needed, immediate implementation
- Cons: Long URLs, limited data size

### Option 2: Backend Storage
- Store calendars in a database (Supabase/Firebase)
- Pros: Scalable, secure, short URLs
- Cons: Requires backend setup, authentication

### Option 3: Hybrid Approach
- Use URL for small calendars, backend for large ones
- Pros: Best of both worlds
- Cons: More complex implementation

## Implementation Plan (Option 1 - URL-Based)

### Phase 1: Data Encoding
1. Create compression utility for calendar data
2. Implement base64 encoding/decoding functions
3. Add URL-safe data serialization

### Phase 2: Share URL Generation
1. Modify `generateShareUrl` to include encoded data
2. Update ShareModal to use new URL format
3. Add URL shortening fallback for long URLs

### Phase 3: Shared Page Updates
1. Update shared page to decode URL data
2. Add fallback to localStorage for backward compatibility
3. Implement error handling for invalid/corrupted data

### Phase 4: Testing & Validation
1. Test with various calendar sizes
2. Verify cross-browser compatibility
3. Test edge cases (expired links, corrupted data)

## Technical Details

### URL Structure
```
/shared/{scheduleId}?data={encodedCalendarData}
```

### Data Compression Strategy
1. Remove redundant fields
2. Use short keys
3. Compress date formats
4. Gzip + base64 encode

### Size Limitations
- URL max length: ~2000 characters (browser-dependent)
- Estimated calendar size: ~500-1000 chars compressed
- Should work for most use cases

## Files to Modify

1. `/src/lib/utils/share.ts`
   - Update `generateShareUrl` to encode calendar data
   - Add compression/encoding utilities

2. `/src/app/shared/[id]/page.tsx`
   - Update `loadSharedSchedule` to decode URL data
   - Add fallback to localStorage
   - Improve error handling

3. `/src/components/ShareModal.tsx`
   - Update to use new share URL format
   - Add loading states for URL generation

## Risks & Mitigations

1. **URL Length**: Monitor URL length, implement shortener if needed
2. **Data Corruption**: Add checksums for data integrity
3. **Browser Compatibility**: Test base64 encoding across browsers
4. **Security**: Don't include sensitive data in URLs

## Success Criteria

- Shared calendar links work across different browsers/devices
- No dependency on recipient's localStorage
- Graceful error handling for invalid links
- Maintains current UI/UX