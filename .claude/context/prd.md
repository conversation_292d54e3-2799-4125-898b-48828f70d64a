# Product Requirements Document (PRD)

## Overview
This document outlines the product requirements and specifications for the Offshore Mate project.

## Project Vision
A Next.js SaaS application providing AI-based learning features with robust authentication and modern UI/UX.

## Current Technology Stack
- **Frontend**: Next.js 15.3.3 with App Router and Turbopack
- **Framework**: React 19 with TypeScript (strict mode)
- **Backend**: Supabase for authentication and database
- **Styling**: Tailwind CSS v4 with PostCSS
- **UI Components**: shadcn/ui components (New York style)
- **Package Manager**: pnpm

## Core Features

### Authentication System
- User registration and login
- Session management via Supabase
- Protected route handling
- Middleware-based session management

### Calendar Features
- Interactive calendar interface
- Export functionality (PNG and PDF)
- Rotation pattern displays
- Shared calendar day alignment
- Responsive design

### UI/UX Requirements
- Modern, clean interface
- Dark mode support
- Responsive design (mobile-first)
- Accessibility compliance (WCAG AA)
- Consistent design system

## Technical Requirements

### Performance
- Fast loading times
- Optimized bundle sizes
- Efficient database queries
- Responsive interactions

### Security
- Secure authentication flows
- Input validation
- SQL injection prevention
- XSS protection

### Scalability
- Modular component architecture
- Efficient state management
- Optimized database schema
- CDN-ready assets

## Success Metrics
- User engagement rates
- Application performance metrics
- Error rates and reliability
- User satisfaction scores

## Future Enhancements
- Additional AI-powered features
- Extended calendar functionality
- Enhanced user management
- Mobile application

---

*This PRD should be updated as the project evolves and new requirements are identified.*