{"permissions": {"allow": ["Bash(npm run dev:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(npm run build:*)", "Bash(git checkout:*)", "Bash(git push:*)", "Bash(npm run lint)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(ls:*)", "mcp__playwright__browser_navigate", "Bash(bun:*)", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_resize", "mcp__playwright__browser_click", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_close", "Bash(git merge:*)", "mcp__playwright__browser_wait_for", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(touch:*)", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_drag", "<PERSON><PERSON>(docker compose:*)", "Bash(git branch:*)", "Bash(npm install)", "Bash(npm install:*)", "Bash(find:*)", "<PERSON><PERSON>(npx playwright:*)", "mcp__playwright__browser_install", "Bash(gh pr merge:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(mv:*)", "Bash(tree:*)", "Bash(ruff:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(cat:*)", "Bash(ruff check:*)", "Bash(pytest:*)", "<PERSON><PERSON>(python:*)", "Bash(python -m pytest:*)", "Bash(python3 -m pytest:*)", "<PERSON><PERSON>(gh issue view:*)", "WebFetch(domain:nearform.com)", "WebFetch(domain:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__serena", "mcp__archon", "<PERSON><PERSON>(curl:*)", "Bash(rg:*)", "Bash(pnpm lint:*)", "Bash(pnpm build:*)", "Bash(pnpm dev:*)", "WebSearch", "mcp__fetch__fetch", "Bash(git log:*)", "Bash(pnpm install:*)", "Bash(cp:*)", "mcp__playwright__browser_hover", "mcp__playwright__browser_press_key", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_type", "Bash(~/.claude-code-docs/claude-docs-helper.sh:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker:*)", "Bash(pnpm tsc:*)"], "deny": []}, "hooks": {"PostToolUse": [{"matcher": "Edit|Write|MultiEdit", "hooks": [{"type": "command", "command": ".claude/hooks/log-tool-usage.sh"}]}]}}