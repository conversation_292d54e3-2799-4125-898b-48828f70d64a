This file is a merged representation of the entire codebase, combining all repository files into a single document.
Generated by <PERSON>opack on: 2024-11-09T17:24:10.990Z

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Repository structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repopack's
  configuration.
- Binary files are not included in this packed representation. Please refer to
  the Repository Structure section for a complete list of file paths, including
  binary files.

## Additional Info

For more information about <PERSON><PERSON><PERSON>, visit: https://github.com/yamadashy/repopack

# Repository Structure
```
.eslintrc.json
.gitignore
components.json
next.config.ts
package.json
postcss.config.mjs
public/file.svg
public/globe.svg
public/next.svg
public/vercel.svg
public/window.svg
README.md
src/app/globals.css
src/app/layout.tsx
src/app/page.tsx
src/components/date-picker-dialog.tsx
src/components/date-picker.tsx
src/components/download-calendar.tsx
src/components/schedule-list.tsx
src/components/ui/button.tsx
src/components/ui/calendar.tsx
src/components/ui/card.tsx
src/components/ui/dialog.tsx
src/components/ui/popover.tsx
src/components/ui/select.tsx
src/lib/utils.ts
src/lib/utils/download.ts
src/lib/utils/index.ts
src/lib/utils/rotation.ts
src/types/rotation.ts
tailwind.config.ts
tsconfig.json
```

# Repository Files

## File: .eslintrc.json
```json
{
  "extends": ["next/core-web-vitals", "next/typescript"]
}
```

## File: .gitignore
```
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
```

## File: components.json
```json
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "src/app/globals.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib",
    "hooks": "@/hooks"
  },
  "iconLibrary": "lucide"
}
```

## File: next.config.ts
```typescript
// next.config.ts
import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  reactStrictMode: true,
}

export default nextConfig
```

## File: package.json
```json
{
  "name": "offshore-calendar-web-1",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "@radix-ui/react-dialog": "^1.1.2",
    "@radix-ui/react-popover": "^1.1.2",
    "@radix-ui/react-select": "^2.1.2",
    "@radix-ui/react-slot": "^1.1.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.1.1",
    "date-fns": "^3.6.0",
    "framer-motion": "^11.11.11",
    "html2canvas": "^1.4.1",
    "lucide-react": "^0.454.0",
    "next": "15.0.3",
    "react": "19.0.0-rc-66855b96-20241106",
    "react-day-picker": "^8.10.1",
    "react-dom": "19.0.0-rc-66855b96-20241106",
    "tailwind-merge": "^2.5.4",
    "tailwindcss-animate": "^1.0.7"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^18",
    "@types/react-dom": "^18",
    "eslint": "^8",
    "eslint-config-next": "15.0.3",
    "postcss": "^8",
    "tailwindcss": "^3.4.1",
    "typescript": "^5"
  }
}
```

## File: postcss.config.mjs
```
/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
  },
};

export default config;
```

## File: public/file.svg
```
<svg fill="none" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M14.5 13.5V5.41a1 1 0 0 0-.3-.7L9.8.29A1 1 0 0 0 9.08 0H1.5v13.5A2.5 2.5 0 0 0 4 16h8a2.5 2.5 0 0 0 2.5-2.5m-1.5 0v-7H8v-5H3v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1M9.5 5V2.12L12.38 5zM5.13 5h-.62v1.25h2.12V5zm-.62 3h7.12v1.25H4.5zm.62 3h-.62v1.25h7.12V11z" clip-rule="evenodd" fill="#666" fill-rule="evenodd"/></svg>
```

## File: public/globe.svg
```
<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><g clip-path="url(#a)"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.27 14.1a6.5 6.5 0 0 0 3.67-3.45q-1.24.21-2.7.34-.31 1.83-.97 3.1M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m.48-1.52a7 7 0 0 1-.96 0H7.5a4 4 0 0 1-.84-1.32q-.38-.89-.63-2.08a40 40 0 0 0 3.92 0q-.25 1.2-.63 2.08a4 4 0 0 1-.84 1.31zm2.94-4.76q1.66-.15 2.95-.43a7 7 0 0 0 0-2.58q-1.3-.27-2.95-.43a18 18 0 0 1 0 3.44m-1.27-3.54a17 17 0 0 1 0 3.64 39 39 0 0 1-4.3 0 17 17 0 0 1 0-3.64 39 39 0 0 1 4.3 0m1.1-1.17q1.45.13 2.69.34a6.5 6.5 0 0 0-3.67-3.44q.65 1.26.98 3.1M8.48 1.5l.01.02q.41.37.84 **********.63 2.08a40 40 0 0 0-3.92 0q.25-1.2.63-2.08a4 4 0 0 1 .85-1.32 7 7 0 0 1 .96 0m-2.75.4a6.5 6.5 0 0 0-3.67 3.44 29 29 0 0 1 2.7-.34q.31-1.83.97-3.1M4.58 6.28q-1.66.16-2.95.43a7 7 0 0 0 0 2.58q1.3.27 2.95.43a18 18 0 0 1 0-3.44m.17 4.71q-1.45-.12-2.69-.34a6.5 6.5 0 0 0 3.67 3.44q-.65-1.27-.98-3.1" fill="#666"/></g><defs><clipPath id="a"><path fill="#fff" d="M0 0h16v16H0z"/></clipPath></defs></svg>
```

## File: public/next.svg
```
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 394 80"><path fill="#000" d="M262 0h68.5v12.7h-27.2v66.6h-13.6V12.7H262V0ZM149 0v12.7H94v20.4h44.3v12.6H94v21h55v12.6H80.5V0h68.7zm34.3 0h-17.8l63.8 79.4h17.9l-32-39.7 32-39.6h-17.9l-23 28.6-23-28.6zm18.3 56.7-9-11-27.1 33.7h17.8l18.3-22.7z"/><path fill="#000" d="M81 79.3 17 0H0v79.3h13.6V17l50.2 62.3H81Zm252.6-.4c-1 0-1.8-.4-2.5-1s-1.1-1.6-1.1-2.6.3-1.8 1-2.5 1.6-1 2.6-1 1.8.3 2.5 1a3.4 3.4 0 0 1 .6 4.3 3.7 3.7 0 0 1-3 1.8zm23.2-33.5h6v23.3c0 2.1-.4 4-1.3 5.5a9.1 9.1 0 0 1-3.8 3.5c-1.6.8-3.5 1.3-5.7 1.3-2 0-3.7-.4-5.3-1s-2.8-1.8-3.7-3.2c-.9-1.3-1.4-3-1.4-5h6c.1.8.3 1.6.7 2.2s1 1.2 1.6 1.5c.7.4 1.5.5 2.4.5 1 0 1.8-.2 2.4-.6a4 4 0 0 0 1.6-1.8c.3-.8.5-1.8.5-3V45.5zm30.9 9.1a4.4 4.4 0 0 0-2-3.3 7.5 7.5 0 0 0-4.3-1.1c-1.3 0-2.4.2-3.3.5-.9.4-1.6 1-2 1.6a3.5 3.5 0 0 0-.3 4c.******* 1.3 1.2l1.8 1 2 .5 3.2.8c1.3.3 2.5.7 3.7 1.2a13 13 0 0 1 3.2 1.8 8.1 8.1 0 0 1 3 6.5c0 2-.5 3.7-1.5 5.1a10 10 0 0 1-4.4 3.5c-1.8.8-4.1 1.2-6.8 1.2-2.6 0-4.9-.4-6.8-1.2-2-.8-3.4-2-4.5-3.5a10 10 0 0 1-1.7-5.6h6a5 5 0 0 0 3.5 4.6c1 .4 2.2.6 3.4.6 1.3 0 2.5-.2 3.5-.6 1-.4 1.8-1 2.4-1.7a4 4 0 0 0 .8-2.4c0-.9-.2-1.6-.7-2.2a11 11 0 0 0-2.1-1.4l-3.2-1-3.8-1c-2.8-.7-5-1.7-6.6-3.2a7.2 7.2 0 0 1-2.4-5.7 8 8 0 0 1 1.7-5 10 10 0 0 1 4.3-3.5c2-.8 4-1.2 6.4-1.2 2.3 0 4.4.4 6.2 1.2 1.8.8 3.2 2 4.3 3.4 1 1.4 1.5 3 1.5 5h-5.8z"/></svg>
```

## File: public/vercel.svg
```
<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1155 1000"><path d="m577.3 0 577.4 1000H0z" fill="#fff"/></svg>
```

## File: public/window.svg
```
<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.5 2.5h13v10a1 1 0 0 1-1 1h-11a1 1 0 0 1-1-1zM0 1h16v11.5a2.5 2.5 0 0 1-2.5 2.5h-11A2.5 2.5 0 0 1 0 12.5zm3.75 4.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5M7 4.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0m1.75.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5" fill="#666"/></svg>
```

## File: README.md
```markdown
This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

NEW build improved calendar download view
```

## File: src/app/globals.css
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    /* ... rest of your CSS variables ... */
  }
}

/* Remove any direct font-family declarations */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
```

## File: src/app/layout.tsx
```typescript
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import localFont from 'next/font/local'
import './globals.css'

// Load Inter font
const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

// Load Dela Gothic One font
const delaGothic = localFont({
  src: '../fonts/DelaGothicOne-Regular.ttf',
  variable: '--font-dela-gothic',
  display: 'swap',
  preload: true,
  fallback: ['system-ui', 'arial'], // Provide fallback fonts
})

export const metadata: Metadata = {
  title: 'Offshore Calendar',
  description: 'Generate offshore work rotation schedules',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${delaGothic.variable}`}>
      <body className={`${inter.className}`}>
        {children}
      </body>
    </html>
  )
}
```

## File: src/app/page.tsx
```typescript
'use client'

import React, { useState } from 'react'
import { ChevronDown, Download, ArrowRight } from 'lucide-react'
import { DatePicker } from "@/components/date-picker"
import { generateRotationCalendar } from '@/lib/utils/rotation'
import { ScheduleList } from '@/components/schedule-list'
import { DownloadCalendar } from '@/components/download-calendar'
import { MonthData, RotationPattern } from '@/types/rotation'
import { downloadCalendarAsImage } from '@/lib/utils/download'

type RotationOption = {
  label: string
  value: string
  workDays: number
  offDays: number
}

export default function Home() {
  const [selectedDate, setSelectedDate] = useState('')
  const [isRotationOpen, setIsRotationOpen] = useState(false)
  const [selectedRotation, setSelectedRotation] = useState('')
  const [isCalendarGenerated, setIsCalendarGenerated] = useState(false)
  const [yearCalendar, setYearCalendar] = useState<MonthData[]>([])
  const [isHovered, setIsHovered] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)

  const rotationOptions: RotationOption[] = [
    { label: '14/14 Rotation', value: '14/14', workDays: 14, offDays: 14 },
    { label: '14/21 Rotation', value: '14/21', workDays: 14, offDays: 21 },
    { label: '21/21 Rotation', value: '21/21', workDays: 21, offDays: 21 },
    { label: '28/28 Rotation', value: '28/28', workDays: 28, offDays: 28 }
  ]

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      const formattedDate = date.toLocaleDateString('en-CA')
      setSelectedDate(formattedDate)
    }
  }

  const handleGenerateCalendar = () => {
    if (!selectedDate || !selectedRotation) {
      alert('Please select both a start date and rotation pattern')
      return
    }
    
    const calendar = generateRotationCalendar(
      new Date(selectedDate),
      selectedRotation as RotationPattern,
      12
    )
    
    setYearCalendar(calendar)
    setIsCalendarGenerated(true)
  }

  const handleDownload = async () => {
    try {
      setIsDownloading(true)
      const filename = `offshore-calendar-${selectedRotation}-${selectedDate}.png`
      await downloadCalendarAsImage('download-calendar', filename)
    } catch (error) {
      console.error('Failed to download calendar:', error)
      alert('Failed to download calendar. Please try again.')
    } finally {
      setIsDownloading(false)
    }
  }

  return (
    <main className="min-h-screen bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-blue-100 via-white to-pink-100 flex items-center justify-center p-4 md:p-8 bg-fixed">
      <div className="relative w-full max-w-[500px]">
        <div className="flex items-center justify-center gap-2 md:gap-3 mb-2">
          <h1 className="text-4xl md:text-5xl lg:text-5xl font-display text-center text-gray-800">
            Offshore Mate
          </h1>
        </div>
        <p className="text-center text-orange-500 mb-8 md:mb-12 tracking-wide uppercase text-[10px] md:text-sm font-light">
          Navigate your offshore schedule with precision
        </p>

        {!isCalendarGenerated ? (
          <div className="space-y-4 md:space-y-6">
            {/* Date Picker Button */}
            <div className="backdrop-blur-xl bg-white/30 rounded-2xl md:rounded-3xl shadow-lg border border-white/30 transition-all duration-300 hover:shadow-xl hover:bg-white/40">
              <div className="px-4 md:px-6 py-3 md:py-4">
                <span className="text-gray-500 text-xs md:text-sm font-medium mb-0.5 md:mb-1 block">
                  Start Date
                </span>
                <DatePicker 
                  date={selectedDate ? new Date(selectedDate) : undefined}
                  onSelect={handleDateSelect}
                />
              </div>
            </div>

            {/* Rotation Selector */}
            <div className="relative">
              <div className="backdrop-blur-xl bg-white/30 rounded-2xl md:rounded-3xl shadow-lg border border-white/30 transition-all duration-300 hover:shadow-xl hover:bg-white/40">
                <button
                  onClick={() => setIsRotationOpen(!isRotationOpen)}
                  className={`flex items-center w-full px-4 md:px-6 py-3 md:py-4 hover:bg-white/10 transition-all duration-200 group
                    ${isRotationOpen ? 'rounded-t-2xl md:rounded-t-3xl' : 'rounded-2xl md:rounded-3xl'}`}
                >
                  <div className="flex-grow text-left">
                    <span className="text-gray-500 text-xs md:text-sm font-medium mb-0.5 md:mb-1 block">Work Rotation</span>
                    <span className="text-gray-800 text-base md:text-lg font-medium group-hover:text-orange-500 transition-colors">
                      {selectedRotation ? `${selectedRotation} Rotation` : 'Select rotation'}
                    </span>
                  </div>
                  <div className="flex items-center justify-center ml-3 md:ml-4">
                    <ChevronDown className={`w-4 h-4 md:w-5 md:h-5 text-gray-400 group-hover:text-orange-500 transition-transform duration-200 
                      ${isRotationOpen ? 'transform rotate-180' : ''}`} />
                  </div>
                </button>

                {/* Rotation Options */}
                {isRotationOpen && (
                  <div className="border-t border-white/30">
                    <div className="backdrop-blur-xl bg-white/80 rounded-b-2xl md:rounded-b-3xl">
                      {rotationOptions.map((option) => (
                        <button
                          key={option.value}
                          onClick={() => {
                            setSelectedRotation(option.value)
                            setIsRotationOpen(false)
                          }}
                          className={`w-full px-4 md:px-6 py-2.5 md:py-3 text-left hover:bg-white/50 transition-all duration-200
                            ${selectedRotation === option.value ? 'bg-white/50' : ''}`}
                        >
                          <div className="text-gray-800 text-sm md:text-base font-medium">{option.label}</div>
                          <div className="text-gray-500 text-xs md:text-sm">
                            {`${option.workDays} days on, ${option.offDays} days off`}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Generate Button */}
            <button
              onClick={handleGenerateCalendar}
              disabled={!selectedDate || !selectedRotation}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              className={`w-full text-white rounded-2xl md:rounded-3xl px-4 md:px-6 py-4 md:py-5 font-medium text-base md:text-lg 
                shadow-lg transition-all duration-300 border relative overflow-hidden
                ${(!selectedDate || !selectedRotation) 
                  ? 'opacity-100 cursor-not-allowed bg-gray-400 border-white/5' 
                  : 'bg-black hover:bg-black/90 hover:scale-[1.02] hover:shadow-xl active:scale-[0.98] active:shadow-md border-white/10'
                }`}
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                Generate Calendar
                <ArrowRight className={`w-4 h-4 md:w-5 md:h-5 transition-transform duration-300 ${isHovered ? 'translate-x-1' : ''}`} />
              </span>
              {selectedDate && selectedRotation && (
                <div 
                  className="absolute inset-0 bg-gradient-to-r from-black to-black/90 transform transition-transform duration-300"
                  style={{
                    transform: isHovered ? 'translateX(0)' : 'translateX(-100%)',
                    zIndex: 0
                  }}
                />
              )}
            </button>
          </div>
        ) : (
          <div className="space-y-6 md:space-y-8">
            <div className="flex justify-between items-center">
              <button
                onClick={() => setIsCalendarGenerated(false)}
                className="bg-white/30 backdrop-blur-xl text-gray-800 rounded-full px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-medium
                  shadow-sm hover:bg-white/40 transition-all duration-200 border border-white/30 group"
              >
                <span className="flex items-center gap-1.5 md:gap-2">
                  <ArrowRight className="w-3.5 h-3.5 md:w-4 md:h-4 rotate-180 group-hover:-translate-x-1 transition-transform" />
                  Back
                </span>
              </button>
              
              <button
                onClick={handleDownload}
                disabled={isDownloading}
                className={`bg-black text-white rounded-full px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-medium
                  shadow-sm hover:bg-black/90 transition-all duration-200 flex items-center gap-1.5 md:gap-2 group
                  ${isDownloading ? 'opacity-75 cursor-wait' : ''}`}
              >
                <Download className={`w-3.5 h-3.5 md:w-4 md:h-4 transition-transform
                  ${isDownloading ? 'animate-bounce' : 'group-hover:translate-y-0.5'}`} 
                />
                {isDownloading ? 'Downloading...' : 'Download Calendar'}
              </button>
            </div>
            
            <div>
              <ScheduleList 
                calendar={yearCalendar} 
                className="h-[calc(100vh-12rem)] overflow-y-auto"
              />
              <DownloadCalendar calendar={yearCalendar} />
            </div>
          </div>
        )}

        {/* Add footer at the bottom */}
        <div className="mt-8 text-center text-sm text-gray-300 tracking-wide">
          <p>
            Created by{' '}
            <a 
              href="https://my-portfolio-r80lxqbzb-ramunasnognys1s-projects.vercel.app/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-gray-300 hover:text-gray-500 underline transition-colors tracking-wide"
            >
              Ramūnas Nognys
            </a>
          </p>
          <p className="mt-1 tracking-wide">Version 1.0.1</p>
        </div>
      </div>
    </main>
  )
}
```

## File: src/components/date-picker-dialog.tsx
```typescript
"use client"

import * as React from "react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Calendar } from "@/components/ui/calendar"

interface DatePickerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selected?: Date
  onSelect: (date: Date | undefined) => void
}

export function DatePickerDialog({
  open,
  onOpenChange,
  selected,
  onSelect,
}: DatePickerDialogProps) {
  const today = new Date()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[400px] p-0">
        <DialogHeader className="sr-only">
          <DialogTitle>Pick a date</DialogTitle>
        </DialogHeader>
        <div className="backdrop-blur-xl bg-white/95 rounded-3xl p-8">
          {/* Today's Date Header */}
          <div className="text-center mb-8">
            <p className="text-lg font-medium text-gray-600">Today</p>
            <p className="text-3xl font-semibold text-gray-800">
              {today.toLocaleDateString('en-US', { 
                month: 'long', 
                day: 'numeric', 
                year: 'numeric' 
              })}
            </p>
          </div>

          {/* Calendar */}
          <div className="px-4">
            <Calendar
              mode="single"
              selected={selected}
              onSelect={onSelect}
              weekStartsOn={0}
              className="w-full"
              fromDate={today}
            />
          </div>

          {/* Selected Date */}
          {selected && (
            <div className="mt-6 text-center text-gray-600">
              Selected: {selected.toLocaleDateString('en-US', {
                month: 'numeric',
                day: 'numeric',
                year: 'numeric'
              })}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
```

## File: src/components/date-picker.tsx
```typescript
"use client"

import * as React from "react"
import { Calendar as CalendarIcon } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface DatePickerProps {
  date?: Date
  onSelect: (date: Date | undefined) => void
}

export function DatePicker({ date, onSelect }: DatePickerProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (date: Date | undefined) => {
    onSelect(date)
    setOpen(false)
  }

  const formatDisplayDate = (date: Date | undefined) => {
    if (!date) return 'Pick your start date'
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button className="flex items-center w-full hover:bg-white/10 transition-all duration-200 group rounded-2xl md:rounded-3xl">
          <div className="flex-grow text-left">
            <span className="text-gray-800 text-base md:text-lg font-medium group-hover:text-orange-500 transition-colors">
              {formatDisplayDate(date)}
            </span>
          </div>
          <div className="flex items-center justify-center ml-3 md:ml-4">
            <CalendarIcon className="w-4 h-4 md:w-5 md:h-5 text-gray-400 group-hover:text-orange-500 transition-colors" />
          </div>
        </button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-white/95 backdrop-blur-md border-0 shadow-lg rounded-3xl
        max-h-screen h-screen sm:h-auto w-screen sm:w-auto m-0 sm:m-4">
        <DialogHeader>
          <DialogTitle className="text-center">
            <p className="text-lg font-medium text-gray-600">Today</p>
            <p className="text-3xl font-semibold text-gray-800">
              {new Date().toLocaleDateString('en-US', { 
                month: 'long', 
                day: 'numeric', 
                year: 'numeric' 
              })}
            </p>
          </DialogTitle>
        </DialogHeader>
        <div className="py-4 w-full flex items-center justify-center">
          <Calendar
            mode="single"
            selected={date}
            onSelect={handleSelect}
            initialFocus
            weekStartsOn={1}
            className="rounded-lg border-0 bg-transparent w-full px-4
              [&_.rdp-caption]:text-xl [&_.rdp-caption]:font-semibold [&_.rdp-caption]:text-gray-800 
              [&_.rdp-months]:w-full
              [&_.rdp-month]:w-full
              [&_tr]:grid [&_tr]:grid-cols-7 [&_tr]:gap-1
              [&_.rdp-head_cell]:flex [&_.rdp-head_cell]:justify-center
              [&_.rdp-head_th]:text-orange-500 [&_.rdp-head_th]:font-medium [&_.rdp-head_th]:h-8 [&_.rdp-head_th]:w-12 [&_.rdp-head_th]:text-base
              [&_.rdp-tbody]:w-full
              [&_.rdp-cell]:flex [&_.rdp-cell]:justify-center
              [&_.rdp-button]:h-12 [&_.rdp-button]:w-12 [&_.rdp-button]:rounded-full 
              [&_.rdp-button]:text-base [&_.rdp-button]:transition-colors [&_.rdp-button]:text-gray-700
              [&_.rdp-button:hover]:bg-white [&_.rdp-button:hover]:shadow-md
              [&_.rdp-day_selected]:bg-blue-500 [&_.rdp-day_selected]:text-white
              [&_.rdp-day_today]:bg-orange-500 [&_.rdp-day_today]:text-white [&_.rdp-day_today]:font-semibold [&_.rdp-day_today]:border [&_.rdp-day_today]:border-orange-500
              [&_.rdp-nav_button]:text-gray-600 [&_.rdp-nav_button:hover]:text-gray-800 [&_.rdp-nav_button]:transition-colors
              [&_.rdp-nav_button]:w-6 [&_.rdp-nav_button]:h-6"
          />
        </div>
        {date && (
          <div className="text-center text-gray-600 pb-2">
            Selected: {date.toLocaleDateString()}
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
```

## File: src/components/download-calendar.tsx
```typescript
// src/components/download-calendar.tsx
import React from 'react';
import { MonthData } from '@/types/rotation';
import { format } from 'date-fns';
import { Plane } from 'lucide-react';

interface DownloadCalendarProps {
  calendar: MonthData[];
}

export function DownloadCalendar({ calendar }: DownloadCalendarProps) {
  // Ensure we only use first 12 months
  const twelveMonths = calendar.slice(0, 12);

  return (
    <div 
      id="download-calendar"
      className="hidden" // Keep it hidden from view but accessible for html2canvas
      style={{
        width: '1080px',
        height: '1920px',
        position: 'fixed',
        top: '-9999px',
        left: '-9999px',
        backgroundColor: 'white',
        padding: '48px',
        fontFamily: 'var(--font-inter)',
      }}
    >
      {/* Title Section */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-4">
          <h1 className="text-5xl font-display text-gray-800">
            Offshore Calendar
          </h1>
        </div>
        <p className="text-orange-500 tracking-wide uppercase text-lg font-light">
          Your Offshore Work Schedule
        </p>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-3 gap-6 auto-rows-fr">
        {twelveMonths.map((month) => (
          <div 
            key={`${month.month}-${month.year}`}
            className="backdrop-blur-xl bg-white rounded-2xl border border-gray-200 shadow-lg p-6"
          >
            <div className="h-full flex flex-col">
              <h3 className="text-xl font-semibold text-gray-800 mb-6">
                {month.month} {month.year}
              </h3>
              
              <div className="flex-grow grid grid-cols-7 gap-1 content-start">
                {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
                  <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                    {day}
                  </div>
                ))}
                
                {Array.from({ length: month.firstDayOfWeek === 0 ? 6 : month.firstDayOfWeek - 1 }).map((_, index) => (
                  <div key={`empty-${index}`} className="aspect-square" />
                ))}
                
                {month.days.map((day, index) => (
                  <div
                    key={index}
                    className={`aspect-square p-1 rounded-lg ${
                      !day.isInRotation
                        ? 'bg-gray-100/50 text-gray-400'
                        : day.isTransitionDay
                          ? 'bg-pink-500/20 text-pink-800 ring-1 ring-pink-500/50'
                          : day.isWorkDay 
                            ? 'bg-orange-500/20 text-orange-800' 
                            : 'bg-green-500/20 text-green-800'
                    }`}
                  >
                    <div className="w-full h-full flex flex-col items-center justify-center rounded-lg text-sm">
                      {day.isTransitionDay && (
                        <Plane className="w-3 h-3 mb-0.5" />
                      )}
                      {format(day.date, 'd')}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer Legend */}
      <div className="fixed bottom-12 left-0 right-0">
        <div className="flex justify-center items-center gap-8">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded bg-orange-500/20" />
            <span className="text-base text-gray-700">Work Days</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded bg-green-500/20" />
            <span className="text-base text-gray-700">Off Days</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded bg-pink-500/20 ring-1 ring-pink-500/50 flex items-center justify-center">
              <Plane className="w-3 h-3" />
            </div>
            <span className="text-base text-gray-700">Transition Days</span>
          </div>
        </div>
      </div>
    </div>
  );
}
```

## File: src/components/schedule-list.tsx
```typescript
// src/components/schedule-list.tsx
import React from 'react';
import { MonthData } from '@/types/rotation';
import { format } from 'date-fns';
import { Plane } from 'lucide-react';

interface ScheduleListProps {
  calendar: MonthData[];
  className?: string;
}

export function ScheduleList({ calendar, className }: ScheduleListProps) {
  return (
    <div 
      id="calendar-container"
      className={`space-y-4 md:space-y-8 pr-2 md:pr-4 ${className}`}
    >
      {calendar.map((month) => (
        <div 
          key={`${month.month}-${month.year}`}
          className="backdrop-blur-xl bg-white rounded-3xl border border-white/20 shadow-lg p-4 md:p-6 pb-2 md:pb-3"
        >
          <div className="h-full flex flex-col">
            <h3 className="text-base md:text-xl font-semibold text-gray-800 mb-6 md:mb-12">
              {month.month} {month.year}
            </h3>
            
            <div className="flex-grow grid grid-cols-7 gap-0.5 md:gap-1 content-start">
              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
                <div key={day} className="text-center text-xs md:text-sm font-medium text-gray-500 py-1 md:py-2">
                  {day}
                </div>
              ))}
              
              {Array.from({ length: month.firstDayOfWeek === 0 ? 6 : month.firstDayOfWeek - 1 }).map((_, index) => (
                <div key={`empty-${index}`} className="aspect-square" />
              ))}
              
              {month.days.map((day, index) => (
                <div
                  key={index}
                  className={`aspect-square p-0.5 md:p-1 rounded-lg ${
                    !day.isInRotation
                      ? 'bg-gray-100/50 text-gray-400'
                      : day.isTransitionDay
                        ? 'bg-pink-500/20 text-pink-800 ring-1 md:ring-2 ring-pink-500/50'
                        : day.isWorkDay 
                          ? 'bg-orange-500/20 text-orange-800' 
                          : 'bg-green-500/20 text-green-800'
                  }`}
                >
                  <div className="w-full h-full flex flex-col items-center justify-center rounded-lg text-xs md:text-sm">
                    {day.isTransitionDay && (
                      <Plane className="w-2 h-2 md:w-3 md:h-3 mb-0.5" />
                    )}
                    {format(day.date, 'd')}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="flex flex-wrap gap-2 md:gap-4 mt-auto pt-4 md:pt-6 pb-2 text-xs md:text-sm">
              <div className="flex items-center gap-1.5 md:gap-2">
                <div className="w-3 h-3 md:w-4 md:h-4 rounded bg-orange-500/20" />
                <span className="py-0.5 md:py-1">Work Days</span>
              </div>
              <div className="flex items-center gap-1.5 md:gap-2">
                <div className="w-3 h-3 md:w-4 md:h-4 rounded bg-green-500/20" />
                <span className="py-0.5 md:py-1">Off Days</span>
              </div>
              <div className="flex items-center gap-1.5 md:gap-2">
                <div className="w-3 h-3 md:w-4 md:h-4 rounded bg-pink-500/20 ring-1 md:ring-2 ring-pink-500/50 flex items-center justify-center">
                  <Plane className="w-2 h-2 md:w-2.5 md:h-2.5" />
                </div>
                <span className="py-0.5 md:py-1">Transition Days</span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
```

## File: src/components/ui/button.tsx
```typescript
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
```

## File: src/components/ui/calendar.tsx
```typescript
"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker } from "react-day-picker"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-sm font-medium",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell:
          "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: cn(
          "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md",
          props.mode === "range"
            ? "[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md"
            : "[&:has([aria-selected])]:rounded-md"
        ),
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "h-8 w-8 p-0 font-normal aria-selected:opacity-100"
        ),
        day_range_start: "day-range-start",
        day_range_end: "day-range-end",
        day_selected:
          "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today: "bg-accent text-accent-foreground",
        day_outside:
          "day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",
        day_disabled: "text-muted-foreground opacity-50",
        day_range_middle:
          "aria-selected:bg-accent aria-selected:text-accent-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: () => <ChevronLeft className="h-4 w-4" />,
        IconRight: () => <ChevronRight className="h-4 w-4" />,
      }}
      {...props}
    />
  )
}
Calendar.displayName = "Calendar"

export { Calendar }
```

## File: src/components/ui/card.tsx
```typescript
import * as React from "react"

import { cn } from "@/lib/utils"

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-xl border bg-card text-card-foreground shadow",
      className
    )}
    {...props}
  />
))
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("font-semibold leading-none tracking-tight", className)}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }
```

## File: src/components/ui/dialog.tsx
```typescript
"use client"

import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

const Dialog = DialogPrimitive.Root

const DialogTrigger = DialogPrimitive.Trigger

const DialogPortal = DialogPrimitive.Portal

const DialogClose = DialogPrimitive.Close

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
))
DialogContent.displayName = DialogPrimitive.Content.displayName

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

export {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
}
```

## File: src/components/ui/popover.tsx
```typescript
"use client"

import * as React from "react"
import * as PopoverPrimitive from "@radix-ui/react-popover"

import { cn } from "@/lib/utils"

const Popover = PopoverPrimitive.Root

const PopoverTrigger = PopoverPrimitive.Trigger

const PopoverAnchor = PopoverPrimitive.Anchor

const PopoverContent = React.forwardRef<
  React.ElementRef<typeof PopoverPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>
>(({ className, align = "center", sideOffset = 4, ...props }, ref) => (
  <PopoverPrimitive.Portal>
    <PopoverPrimitive.Content
      ref={ref}
      align={align}
      sideOffset={sideOffset}
      className={cn(
        "z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className
      )}
      {...props}
    />
  </PopoverPrimitive.Portal>
))
PopoverContent.displayName = PopoverPrimitive.Content.displayName

export { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }
```

## File: src/components/ui/select.tsx
```typescript
"use client"

import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { Check, ChevronDown, ChevronUp } from "lucide-react"

import { cn } from "@/lib/utils"

const Select = SelectPrimitive.Root

const SelectGroup = SelectPrimitive.Group

const SelectValue = SelectPrimitive.Value

const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
      className
    )}
    {...props}
  >
    {children}
    <SelectPrimitive.Icon asChild>
      <ChevronDown className="h-4 w-4 opacity-50" />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
))
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName

const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronUp className="h-4 w-4" />
  </SelectPrimitive.ScrollUpButton>
))
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName

const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      "flex cursor-default items-center justify-center py-1",
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
))
SelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName

const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = "popper", ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        "relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        position === "popper" &&
          "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      )}
      position={position}
      {...props}
    >
      <SelectScrollUpButton />
      <SelectPrimitive.Viewport
        className={cn(
          "p-1",
          position === "popper" &&
            "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
      <SelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
))
SelectContent.displayName = SelectPrimitive.Content.displayName

const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn("px-2 py-1.5 text-sm font-semibold", className)}
    {...props}
  />
))
SelectLabel.displayName = SelectPrimitive.Label.displayName

const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </SelectPrimitive.ItemIndicator>
    </span>
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
))
SelectItem.displayName = SelectPrimitive.Item.displayName

const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 my-1 h-px bg-muted", className)}
    {...props}
  />
))
SelectSeparator.displayName = SelectPrimitive.Separator.displayName

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
}
```

## File: src/lib/utils.ts
```typescript
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

## File: src/lib/utils/download.ts
```typescript
// src/lib/utils/download.ts
import html2canvas from 'html2canvas';

export async function downloadCalendarAsImage(elementId: string, filename: string): Promise<void> {
  try {
    const element = document.getElementById('download-calendar');
    if (!element) {
      throw new Error('Download calendar element not found');
    }

    // Make the element visible temporarily
    const originalDisplay = element.style.display;
    element.style.display = 'block';

    // Configure html2canvas options
    const canvas = await html2canvas(element, {
      scale: 2, // Increase quality
      useCORS: true,
      backgroundColor: '#ffffff',
      logging: false,
      width: 1080,
      height: 1920,
    });

    // Hide the element again
    element.style.display = originalDisplay;

    // Convert to blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob as Blob);
      }, 'image/png', 1.0);
    });

    // Create download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading calendar:', error);
    throw error;
  }
}
```

## File: src/lib/utils/index.ts
```typescript
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string | undefined, options: Intl.DateTimeFormatOptions = {
  month: 'long',
  day: 'numeric',
  year: 'numeric',
}): string {
  if (!date) return ''
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('en-US', options)
}

export function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate()
}

export function getFirstDayOfMonth(year: number, month: number): number {
  return new Date(year, month, 1).getDay()
}

export function isToday(date: Date): boolean {
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

export function isSameDay(date1: Date, date2: Date): boolean {
  return date1.toDateString() === date2.toDateString()
}

export function addDays(date: Date, days: number): Date {
  const result = new Date(date)
  result.setDate(result.getDate() + days)
  return result
}

export function getDatesBetween(startDate: Date, endDate: Date): Date[] {
  const dates: Date[] = []
  const currentDate = new Date(startDate)

  while (currentDate <= endDate) {
    dates.push(new Date(currentDate))
    currentDate.setDate(currentDate.getDate() + 1)
  }

  return dates
}

export function downloadFile(content: string, filename: string, contentType = 'text/plain'): void {
  const blob = new Blob([content], { type: contentType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

export function padNumber(num: number): string {
  return num.toString().padStart(2, '0')
}

export function debounce<T extends (...args: Parameters<T>) => ReturnType<T>>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }

    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(later, wait)
  }
}

export function groupBy<T>(array: T[], key: keyof T | ((item: T) => string)): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : String(item[key])
    return {
      ...groups,
      [groupKey]: [...(groups[groupKey] || []), item],
    }
  }, {} as Record<string, T[]>)
}
```

## File: src/lib/utils/rotation.ts
```typescript
import { RotationConfig, RotationPattern, MonthData, CalendarDay } from '@/types/rotation';
import { addDays, startOfMonth, endOfMonth, format, getDay, differenceInDays, isSameDay, addWeeks } from 'date-fns';

export const rotationConfigs: Record<RotationPattern, RotationConfig> = {
  '14/14': { workDays: 15, offDays: 13, label: '14/14 Rotation', value: '14/14' },
  '14/21': { workDays: 15, offDays: 20, label: '14/21 Rotation', value: '14/21' },
  '21/21': { workDays: 22, offDays: 20, label: '21/21 Rotation', value: '21/21' },
  '28/28': { workDays: 29, offDays: 27, label: '28/28 Rotation', value: '28/28' },
};

function convertToMondayBasedDay(day: number): number {
  return day === 0 ? 7 : day;
}

export function generateRotationCalendar(
  startDate: Date,
  pattern: RotationPattern,
  months: number = 12
): MonthData[] {
  const monthData: MonthData[] = [];
  const config = rotationConfigs[pattern];
  
  let currentDate = new Date(startDate);
  const endDate = addDays(currentDate, months * 31);
  
  // Calculate all work periods and transition dates
  const workPeriods: { start: Date; end: Date }[] = [];
  const transitionDates: Date[] = [];
  let periodStart = new Date(startDate);
  
  while (periodStart < endDate) {
    // Calculate end of current work period
    const periodEnd = addWeeks(periodStart, Math.floor((config.workDays - 1) / 7));
    
    // Store work period
    workPeriods.push({
      start: new Date(periodStart),
      end: new Date(periodEnd)
    });
    
    // Add transition dates
    transitionDates.push(new Date(periodStart)); // Start transition
    transitionDates.push(new Date(periodEnd));   // End transition
    
    // Move to next period start
    periodStart = addDays(periodEnd, config.offDays + 1);
  }
  
  while (currentDate < endDate) {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(monthStart);
    const days: CalendarDay[] = [];
    
    let dayPointer = monthStart;
    while (dayPointer <= monthEnd) {
      const daysSinceStart = differenceInDays(dayPointer, startDate);
      
      if (daysSinceStart < 0) {
        days.push({
          date: new Date(dayPointer),
          isWorkDay: false,
          isInRotation: false,
          isTransitionDay: false
        });
      } else {
        // Check if the current day falls within any work period
        const isInWorkPeriod = workPeriods.some(period => 
          (dayPointer >= period.start && dayPointer <= period.end)
        );
        
        // Check if it's a transition day
        const isTransitionDay = transitionDates.some(date => 
          isSameDay(dayPointer, date)
        );
        
        days.push({
          date: new Date(dayPointer),
          isWorkDay: isInWorkPeriod,
          isInRotation: true,
          isTransitionDay
        });
      }
      
      dayPointer = addDays(dayPointer, 1);
    }
    
    const sundayBasedFirstDay = getDay(monthStart);
    const mondayBasedFirstDay = convertToMondayBasedDay(sundayBasedFirstDay);
    
    monthData.push({
      month: format(monthStart, 'MMMM'),
      year: monthStart.getFullYear(),
      days,
      firstDayOfWeek: mondayBasedFirstDay
    });
    
    currentDate = addDays(monthEnd, 1);
  }
  
  return monthData;
}
```

## File: src/types/rotation.ts
```typescript
export type RotationPattern = '14/14' | '14/21' | '21/21' | '28/28';

export interface RotationConfig {
  workDays: number;
  offDays: number;
  label: string;
  value: RotationPattern;
}

export interface CalendarDay {
  date: Date;
  isWorkDay: boolean;
  isInRotation: boolean;
  isTransitionDay: boolean;
}

export interface MonthData {
  month: string;
  year: number;
  days: CalendarDay[];
  firstDayOfWeek: number;
}
```

## File: tailwind.config.ts
```typescript
import type { Config } from "tailwindcss";

export default {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		fontFamily: {
  			sans: ['var(--font-inter)', 'system-ui', 'sans-serif'],
  			display: ['var(--font-dela-gothic)', 'system-ui', 'sans-serif'],
  		},
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
```

## File: tsconfig.json
```json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```
