@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animation for notifications */
@keyframes fadeInOut {
  0% { opacity: 0; transform: translateY(-10px); }
  10% { opacity: 1; transform: translateY(0); }
  90% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-10px); }
}

.animate-fade-in-out {
  animation: fadeInOut 3s ease-in-out;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Bottom sheet animations */
@keyframes slide-in-from-bottom {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-out-to-bottom {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

.animate-in {
  animation-duration: 300ms;
  animation-fill-mode: both;
}

.slide-in-from-bottom-full {
  animation-name: slide-in-from-bottom;
}

/* Touch feedback for mobile */
.touch-manipulation {
  touch-action: manipulation;
}


/* Touch-safe zones for navigation buttons */
.navigation-buttons {
  position: relative;
  z-index: 10;
  pointer-events: auto;
}

.navigation-buttons button {
  position: relative;
  pointer-events: auto;
  /* Ensure minimum touch target size */
  min-width: 44px;
  min-height: 44px;
}

/* Mobile calendar specific styles */
@media (max-width: 768px) {
  #calendar-container {
    /* Always reserve space so the fixed toolbar never overlaps */
    padding-bottom: calc(var(--bottom-toolbar-total-height) + var(--bottom-toolbar-buffer));
  }
  
  /* Ensure last calendar month has enough space */
  #calendar-container > div:last-child {
    margin-bottom: calc(var(--bottom-toolbar-total-height) + 1rem);
  }
  
  /* Chrome-specific fix for mobile */
  @supports (-webkit-appearance: none) and (not (-ms-ime-align: auto)) {
    #calendar-container {
      padding-bottom: calc(var(--bottom-toolbar-total-height) + var(--bottom-toolbar-buffer) + 1rem);
    }
  }
}

/* Progress dots touch improvements */
.progress-dots {
  position: relative;
  z-index: 20;
  pointer-events: auto;
}

.progress-dots button {
  position: relative;
  z-index: 21;
  pointer-events: auto;
  /* Ensure minimum touch target size for accessibility */
  min-width: 24px;
  min-height: 24px;
}


/* Fix for mobile dialog pointer events */
[role="dialog"] * {
  pointer-events: auto !important;
}

[data-radix-dialog-content] * {
  pointer-events: auto !important;
}

/* Today cell pulse animation */
@keyframes todayPulse {
  0% { 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
    transform: scale(1.02);
  }
  100% { 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    transform: scale(1);
  }
}

@keyframes todayGradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.today-cell {
  animation: todayPulse 2s ease-in-out infinite;
  background: linear-gradient(-45deg, #3b82f6, #6366f1, #8b5cf6, #a855f7);
  background-size: 400% 400%;
  animation: todayPulse 2s ease-in-out infinite, todayGradient 3s ease infinite;
}

.today-badge {
  background: linear-gradient(135deg, #1e40af, #3730a3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Modern Current Date Styling */
@keyframes todayGlowModern {
  0% { 
    box-shadow: 
      0 0 0 0 rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% { 
    box-shadow: 
      0 0 0 4px rgba(59, 130, 246, 0.2),
      0 0 30px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
  100% { 
    box-shadow: 
      0 0 0 0 rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

@keyframes todayPulseModern {
  0% { 
    transform: scale(1);
  }
  50% { 
    transform: scale(1.05);
  }
  100% { 
    transform: scale(1);
  }
}

@keyframes todayShimmer {
  0% { 
    background-position: -200% 0;
  }
  100% { 
    background-position: 200% 0;
  }
}

.today-modern {
  position: relative;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  color: white !important;
  font-weight: 700 !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: todayGlowModern 3s ease-in-out infinite, todayPulseModern 2s ease-in-out infinite;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  z-index: 10;
}

.today-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -200%;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: todayShimmer 3s infinite;
}

.today-modern:hover {
  transform: scale(1.1) !important;
  box-shadow: 
    0 0 0 6px rgba(59, 130, 246, 0.3),
    0 0 40px rgba(59, 130, 246, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.4);
  animation-play-state: paused;
}

.today-modern:active {
  transform: scale(0.95) !important;
  transition: transform 0.1s ease;
}

.today-modern:focus-visible {
  outline: 3px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .today-modern {
    font-size: 0.875rem;
    animation: todayGlowModern 3s ease-in-out infinite;
  }
  
  .today-modern:hover {
    transform: scale(1.08) !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .today-modern {
    animation: none;
  }
  
  .today-modern::before {
    animation: none;
  }
  
  .today-modern:hover {
    animation: none;
  }
}

/* Bottom sheet animations for mobile date picker */
@keyframes slide-in-from-bottom-full {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-out-to-bottom-full {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

.slide-in-from-bottom {
  animation: slide-in-from-bottom 0.3s ease-out;
}

.slide-out-to-bottom {
  animation: slide-out-to-bottom 0.3s ease-out;
}

/* Better bottom sheet styles */
.bottom-sheet {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
  box-shadow: 
    0 -10px 25px -5px rgba(0, 0, 0, 0.1),
    0 -4px 6px -2px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.bottom-sheet-handle {
  width: 48px;
  height: 6px;
  background: rgba(156, 163, 175, 0.6);
  border-radius: 9999px;
  transition: all 0.2s ease;
}

.bottom-sheet-handle:hover {
  background: rgba(156, 163, 175, 0.8);
  transform: scaleX(1.2);
}

/* Hide scrollbars */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Pressed state for buttons */
@layer utilities {
  .active\:scale-\[0\.98\]:active {
    transform: scale(0.98);
  }
  
  /* Smooth transitions for calendar cells */
  .transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }
  
  /* Custom shadow for glass-morphism cards */
  .shadow-card {
    box-shadow: 
      0 0 0 1px rgba(255, 255, 255, 0.1) inset,
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-card-hover {
    box-shadow: 
      0 0 0 1px rgba(255, 255, 255, 0.2) inset,
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 25px 30px -5px rgba(0, 0, 0, 0.12),
      0 15px 15px -5px rgba(0, 0, 0, 0.06);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
 
    --radius: 0.5rem;
    --bottom-toolbar-height: 5rem; /* 80px - actual height with content */
    --bottom-toolbar-safe-area: env(safe-area-inset-bottom, 0);
    --bottom-toolbar-total-height: calc(var(--bottom-toolbar-height) + var(--bottom-toolbar-safe-area));
    --bottom-toolbar-buffer: 2rem; /* Extra buffer for Chrome */
    
    /* Safe area insets for all edges */
    --safe-area-inset-top: env(safe-area-inset-top, 0);
    --safe-area-inset-right: env(safe-area-inset-right, 0);
    --safe-area-inset-bottom: env(safe-area-inset-bottom, 0);
    --safe-area-inset-left: env(safe-area-inset-left, 0);
    
    /* Dynamic viewport height for mobile browsers */
    --viewport-height: 100vh;
    --viewport-height-dynamic: 100dvh;
    
    /* Fallback for browsers that don't support dvh */
    @supports (height: 100dvh) {
      --viewport-height: 100dvh;
    }
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Safe area padding for main scrollable content */
  main.overflow-y-auto {
    padding-bottom: calc(var(--bottom-toolbar-height) + var(--bottom-toolbar-safe-area) + 1rem);
  }

  /* Mobile browser height fix with modern viewport units */
  .flex-col.h-screen {
    height: 100vh;
    height: var(--viewport-height-dynamic, 100vh);
    min-height: -webkit-fill-available;
  }
  
  /* Safe area padding utilities */
  .safe-area-top {
    padding-top: var(--safe-area-inset-top);
  }
  
  .safe-area-inset-x {
    padding-left: var(--safe-area-inset-left);
    padding-right: var(--safe-area-inset-right);
  }
  
  /* Mobile-specific safe area adjustments */
  @media (max-width: 768px) {
    .mobile-safe-top {
      padding-top: max(1rem, var(--safe-area-inset-top));
    }
  }
  
  /* Mobile calendar container specific fixes */
  @supports (height: 100dvh) {
    .mobile-calendar-container {
      padding-bottom: calc(var(--bottom-toolbar-total-height) + 1rem);
    }
  }
  
  /* Ensure bottom toolbar doesn't overlap content on mobile */
  .has-bottom-toolbar {
    padding-bottom: calc(var(--bottom-toolbar-total-height) + var(--bottom-toolbar-buffer));
  }
  
  /* Fix for Chrome mobile toolbar positioning */
  .fixed.bottom-0 {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
  }

/* Prevent invisible fixed wrappers from blocking taps in Chrome.
   The inner visible panels explicitly set pointer-events back to auto. */
.fixed.bottom-0.pointer-events-none {
  pointer-events: none;
}
  
  /* Bottom toolbar should be fully interactive.
     Removed previous pointer-events overrides that accidentally blocked
     clicks on labels and other controls inside the toolbar. */
  
  /* Fix for mobile browsers with dynamic viewport */
  @media (max-width: 768px) {
    .mobile-safe-area {
      padding-bottom: calc(var(--bottom-toolbar-total-height) + env(safe-area-inset-bottom, 0));
    }
  }
}